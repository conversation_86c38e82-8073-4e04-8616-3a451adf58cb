import React, { useEffect, useState } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Checkbox } from '@/components/ui/checkbox'
import { useForm } from 'react-hook-form'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'

const SALE_END_DATE = new Date('2025-07-01T23:59:59').getTime()

const formSchema = z.object({
  name: z.string().min(2, 'Name is required'),
  email: z.string().email('Invalid email address'),
  phone: z.string().min(10, 'Phone number is required'),
  recurring: z.boolean().optional()
})

const packages = []

export default function PricingPage() {
  const [now, setNow] = useState(Date.now())
  const [open, setOpen] = useState(false)
  const [selectedPlan, setSelectedPlan] = useState(null)

  const {
    register,
    handleSubmit,
    formState: { errors }
  } = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      recurring: false
    }
  })

  const onSubmit = () => {
    window.open('https://paystack.shop/pay/ri42zx2eku', '_blank')
  }

  useEffect(() => {
    const interval = setInterval(() => setNow(Date.now()), 1000)
    return () => clearInterval(interval)
  }, [])

  const timeLeft = SALE_END_DATE - now
  const expired = timeLeft <= 0

  const countdown = expired
    ? null
    : `${Math.floor(timeLeft / (1000 * 60 * 60 * 24))}d ${Math.floor(
        (timeLeft % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)
      )}h ${Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60))}m`

  const setOpenForm = (pkg) => {
    setSelectedPlan(pkg)
    setOpen(true)
  }

  return (
    <>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-4 md:p-8 bg-green-50">
        {packages.map((pkg, idx) => (
          <Card
            key={idx}
            className={`relative rounded-xl shadow-md border border-green-300 transition-transform transform hover:-translate-y-1 hover:shadow-xl bg-white ${
              pkg.highlight ? 'ring-4 ring-green-700' : ''
            }`}
          >
            {!expired && pkg.oldPrice && (
              <div className="absolute top-0 left-0 bg-red-600 text-white text-xs font-semibold px-3 py-1 rounded-br-xl animate-pulse shadow-lg">
                🎉 Promo Ends In: {countdown}
              </div>
            )}

            <CardContent className="px-5 pt-4 pb-6 flex flex-col gap-3">
              <div className="flex flex-col mt-1">
                <h2 className="text-lg font-bold text-green-900 mt-2">
                  {pkg.name}
                </h2>
                <p className="text-sm text-green-700 mt-1">{pkg.subtitle}</p>
              </div>

              <div className="space-y-1">
                {pkg.oldPrice && !expired && (
                  <p className="text-sm line-through text-red-400 font-semibold">
                    {pkg.oldPrice}
                  </p>
                )}
                <p className="text-2xl font-extrabold text-green-800">
                  {expired || !pkg.oldPrice ? pkg.salePrice : pkg.salePrice}
                </p>
                {pkg.renewal && (
                  <p className="text-xs text-gray-500 font-medium">
                    Annual Renewal: {pkg.renewal}
                  </p>
                )}
              </div>

              <ul className="text-sm text-gray-800 list-disc list-inside space-y-1">
                {pkg.features.map((feature, i) => (
                  <li key={i}>{feature}</li>
                ))}
              </ul>

              {pkg.monthly && (
                <div className="mt-3 pt-3 border-t text-sm text-green-700 font-medium">
                  Optional:{' '}
                  <span className="font-semibold">+ {pkg.monthly}/month</span>{' '}
                  maintenance & updates
                </div>
              )}

              <Button
                className="mt-4 bg-green-600 hover:bg-green-700 text-white w-full"
                onClick={() => setOpenForm(pkg)}
              >
                {pkg.cta}
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>

      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{selectedPlan?.name}</DialogTitle>
          </DialogHeader>
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
            <Input placeholder="Your Full Name" {...register('name')} />
            {errors.name && (
              <p className="text-red-500 text-xs">{errors.name.message}</p>
            )}

            <Input placeholder="Your Email Address" {...register('email')} />
            {errors.email && (
              <p className="text-red-500 text-xs">{errors.email.message}</p>
            )}

            <Input placeholder="Phone Number" {...register('phone')} />
            {errors.phone && (
              <p className="text-red-500 text-xs">{errors.phone.message}</p>
            )}

            <div className="flex items-center gap-2">
              <Checkbox {...register('recurring')} />
              <label className="text-sm text-gray-700">
                I want to subscribe to monthly maintenance
              </label>
            </div>

            <Button
              type="submit"
              className="w-full bg-green-700 text-white hover:bg-green-800"
            >
              Proceed to Pay
            </Button>
          </form>
        </DialogContent>
      </Dialog>
    </>
  )
}
