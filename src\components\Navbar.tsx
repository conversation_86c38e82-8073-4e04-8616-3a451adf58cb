
import React, { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { Menu, X } from 'lucide-react';
import { Link, useLocation } from 'react-router-dom';

const Navbar = () => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const location = useLocation();

  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 50) {
        setIsScrolled(true);
      } else {
        setIsScrolled(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const isActive = (path: string) => {
    if (path === '/' && location.pathname === '/') return true;
    if (path !== '/' && location.pathname.startsWith(path)) return true;
    return false;
  };

  const navItems = [
    { name: 'Home', path: '/' },
    { name: 'Services', path: '/services' },
    { name: 'Work', path: '/work' },
    { name: 'Process', path: '/process' },
    { name: 'About', path: '/about' },
    { name: 'Careers', path: '/#careers' },
  ];

  return (
    <header
      className={cn(
        'fixed top-0 left-0 right-0 z-50 transition-all duration-300 w-full',
        isScrolled
          ? 'bg-agency-darker bg-opacity-95 shadow-md py-3'
          : 'bg-transparent py-5'
      )}
    >
      <div className="container mx-auto px-4 md:px-6">
        <div className="flex items-center justify-between">
          <Link to="/" className="flex items-center">
            <div className="w-10 h-10 rounded bg-agency-green flex items-center justify-center mr-2">
              <span className="text-agency-dark font-bold text-xl">K</span>
            </div>
            <span className="text-white font-bold text-xl hidden sm:block">
              KavaraDigital
            </span>
          </Link>

          <nav className="hidden md:flex items-center space-x-8">
            {navItems.map((item) => {
              const isNavItemActive = isActive(item.path)
              return (
                <Link
                  key={item.name}
                  to={item.path}
                  className={cn(
                    'text-sm transition-colors duration-200',
                    isNavItemActive
                      ? 'text-agency-green font-medium'
                      : 'text-agency-white-muted hover:text-agency-green'
                  )}
                >
                  {item.name}
                </Link>
              )
            })}
          </nav>

          <Link
            to="/contact"
            className="hidden md:flex px-5 py-2 bg-agency-green text-agency-dark font-medium rounded-md hover:bg-opacity-90 transition-all btn-glow"
          >
            Contact Us
          </Link>

          <button
            className="md:hidden text-white"
            onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
          >
            {mobileMenuOpen ? <X size={24} /> : <Menu size={24} />}
          </button>
        </div>
      </div>

      {/* Mobile Menu */}
      <div
        className={cn(
          'fixed inset-x-0 top-[56px] z-50 bg-agency-darker transform transition-transform duration-300 ease-in-out md:hidden',
          mobileMenuOpen ? 'translate-y-0' : '-translate-y-full'
        )}
      >
        <div className="px-4 py-6 space-y-4">
          {navItems.map((item) => (
            <Link
              key={item.name}
              to={item.path}
              className={cn(
                'block py-2 transition-colors duration-200',
                isActive(item.path)
                  ? 'text-agency-green font-medium'
                  : 'text-agency-white-muted hover:text-agency-green'
              )}
              onClick={() => setMobileMenuOpen(false)}
            >
              {item.name}
            </Link>
          ))}
          <Link
            to="/contact"
            className="block w-full text-center py-3 mt-4 bg-agency-green text-agency-dark font-medium rounded-md hover:bg-opacity-90 transition-all"
            onClick={() => setMobileMenuOpen(false)}
          >
            Contact Us
          </Link>
        </div>
      </div>
    </header>
  )
};

export default Navbar;
